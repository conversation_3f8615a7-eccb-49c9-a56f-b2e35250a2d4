{"name": "gsm-landing-page", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "vercel-build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "deploy": "npm run build && tcb framework deploy", "deploy:cloudbase": "tcb framework deploy", "login:cloudbase": "tcb login"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "leaflet": "^1.9.4", "npm-run-all2": "^8.0.4", "pinia": "^3.0.3", "typescript": "~5.8.0", "vite": "^7.0.6", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-tsc": "^3.0.4", "vue3-puzzle-vcode": "^1.1.7"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/leaflet": "^1.9.20", "@types/node": "^22.16.5", "@vue/tsconfig": "^0.7.0", "vite-plugin-vue-devtools": "^8.0.0"}}